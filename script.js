class ImageCompressor {
    constructor() {
        this.selectedFiles = [];
        this.compressedImages = [];
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // File input and upload area
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');

        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => this.handleFileSelect(e.target.files));

        // Drag and drop
        uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        uploadArea.addEventListener('drop', this.handleDrop.bind(this));

        // Controls
        document.getElementById('qualitySlider').addEventListener('input', this.updateQualityValue);
        document.getElementById('resizeToggle').addEventListener('change', this.toggleResizeControls);
        document.getElementById('maintainAspect').addEventListener('change', this.handleAspectRatioToggle);
        document.getElementById('widthInput').addEventListener('input', this.handleDimensionChange.bind(this, 'width'));
        document.getElementById('heightInput').addEventListener('input', this.handleDimensionChange.bind(this, 'height'));

        // Action buttons
        document.getElementById('compressBtn').addEventListener('click', this.compressImages.bind(this));
        document.getElementById('clearBtn').addEventListener('click', this.clearAll.bind(this));
        document.getElementById('downloadAllBtn').addEventListener('click', this.downloadAll.bind(this));
    }

    handleFileSelect(files) {
        const validFiles = Array.from(files).filter(file => file.type.startsWith('image/'));

        if (validFiles.length === 0) {
            this.showToast('Please select valid image files', 'error');
            return;
        }

        this.selectedFiles = validFiles;
        this.displaySelectedImages();
        this.showControls();
    }

    handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.currentTarget.classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
        this.handleFileSelect(e.dataTransfer.files);
    }

    updateQualityValue(e) {
        document.getElementById('qualityValue').textContent = e.target.value;
    }

    toggleResizeControls(e) {
        const resizeControls = document.getElementById('resizeControls');
        resizeControls.style.display = e.target.checked ? 'block' : 'none';

        // Clear inputs when toggling off
        if (!e.target.checked) {
            document.getElementById('widthInput').value = '';
            document.getElementById('heightInput').value = '';
        }
    }

    handleAspectRatioToggle() {
        // Clear dimension inputs when aspect ratio is toggled
        if (document.getElementById('maintainAspect').checked) {
            document.getElementById('heightInput').value = '';
        }
    }

    handleDimensionChange(dimension, e) {
        if (!document.getElementById('maintainAspect').checked) return;

        const value = parseInt(e.target.value);
        if (!value || !this.selectedFiles.length) return;

        // Calculate aspect ratio from first image
        const firstFile = this.selectedFiles[0];
        const img = new Image();
        img.onload = () => {
            const aspectRatio = img.width / img.height;

            if (dimension === 'width') {
                document.getElementById('heightInput').value = Math.round(value / aspectRatio);
            } else {
                document.getElementById('widthInput').value = Math.round(value * aspectRatio);
            }
        };
        img.src = URL.createObjectURL(firstFile);
    }

    displaySelectedImages() {
        const imagesGrid = document.getElementById('imagesGrid');
        imagesGrid.innerHTML = '';
        imagesGrid.style.display = 'grid';

        this.selectedFiles.forEach((file, index) => {
            const imageCard = this.createImageCard(file, index);
            imagesGrid.appendChild(imageCard);
        });
    }

    createImageCard(file, index) {
        const card = document.createElement('div');
        card.className = 'image-card fade-in';

        const img = document.createElement('img');
        img.className = 'image-preview';
        img.src = URL.createObjectURL(file);

        card.innerHTML = `
            <img class="image-preview" src="${img.src}" alt="${file.name}">
            <div class="image-info">
                <span class="image-name">${file.name}</span>
                <span class="image-size">${this.formatFileSize(file.size)}</span>
            </div>
            <div class="compression-info" id="compressionInfo-${index}" style="display: none;">
                <span class="original-size">Original: <span id="originalSize-${index}"></span></span>
                <span class="compressed-size">Compressed: <span id="compressedSize-${index}"></span></span>
                <span class="savings">Saved: <span id="savings-${index}"></span></span>
            </div>
            <button class="download-btn" id="downloadBtn-${index}" style="display: none;">
                <i class="fas fa-download"></i>
                Download
            </button>
        `;

        return card;
    }

    showControls() {
        document.getElementById('controlsSection').style.display = 'block';
        document.getElementById('controlsSection').classList.add('fade-in');
    }

    async compressImages() {
        if (this.selectedFiles.length === 0) return;

        const compressBtn = document.getElementById('compressBtn');
        compressBtn.disabled = true;
        compressBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Compressing...';

        this.showProgressModal();
        this.compressedImages = [];

        for (let i = 0; i < this.selectedFiles.length; i++) {
            try {
                const compressedBlob = await this.compressImage(this.selectedFiles[i]);
                this.compressedImages.push({
                    blob: compressedBlob,
                    originalFile: this.selectedFiles[i],
                    index: i
                });

                this.updateImageCard(i, this.selectedFiles[i], compressedBlob);
                this.updateProgress(i + 1, this.selectedFiles.length);
            } catch (error) {
                console.error('Error compressing image:', error);
                this.showToast(`Error compressing ${this.selectedFiles[i].name}`, 'error');
            }
        }

        this.hideProgressModal();
        this.showDownloadSection();

        compressBtn.disabled = false;
        compressBtn.innerHTML = '<i class="fas fa-compress"></i> Compress Images';

        this.showToast(`Successfully compressed ${this.compressedImages.length} images!`);
    }

    async compressImage(file) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                // Get compression settings
                const quality = document.getElementById('qualitySlider').value / 100;
                const format = document.getElementById('formatSelect').value;
                const shouldResize = document.getElementById('resizeToggle').checked;

                let { width, height } = this.calculateDimensions(img, shouldResize);

                // Debug logging
                console.log(`Original: ${img.width}x${img.height}, Target: ${width}x${height}, Resize: ${shouldResize}`);

                canvas.width = width;
                canvas.height = height;

                // Clear canvas and draw image
                ctx.clearRect(0, 0, width, height);
                ctx.drawImage(img, 0, 0, width, height);

                const outputFormat = format === 'original' ? file.type : `image/${format}`;

                canvas.toBlob(resolve, outputFormat, quality);
            };

            img.src = URL.createObjectURL(file);
        });
    }

    calculateDimensions(img, shouldResize) {
        if (!shouldResize) {
            return { width: img.width, height: img.height };
        }

        const widthInput = document.getElementById('widthInput').value;
        const heightInput = document.getElementById('heightInput').value;
        const maintainAspect = document.getElementById('maintainAspect').checked;

        // If no dimensions specified, return original
        if (!widthInput && !heightInput) {
            console.log('No resize dimensions specified, using original size');
            return { width: img.width, height: img.height };
        }

        let width = widthInput ? parseInt(widthInput) : null;
        let height = heightInput ? parseInt(heightInput) : null;

        // Validate input values
        if (width && width <= 0) width = null;
        if (height && height <= 0) height = null;

        if (maintainAspect) {
            const aspectRatio = img.width / img.height;

            if (width && !height) {
                height = Math.round(width / aspectRatio);
            } else if (height && !width) {
                width = Math.round(height * aspectRatio);
            } else if (width && height) {
                // If both are specified, prioritize width and recalculate height
                height = Math.round(width / aspectRatio);
            }
        }

        // Ensure we have valid dimensions
        const finalWidth = width && width > 0 ? width : img.width;
        const finalHeight = height && height > 0 ? height : img.height;

        console.log(`Resize calculation: ${img.width}x${img.height} -> ${finalWidth}x${finalHeight}`);

        return {
            width: finalWidth,
            height: finalHeight
        };
    }

    updateImageCard(index, originalFile, compressedBlob) {
        const compressionInfo = document.getElementById(`compressionInfo-${index}`);
        const downloadBtn = document.getElementById(`downloadBtn-${index}`);

        const originalSize = originalFile.size;
        const compressedSize = compressedBlob.size;
        const savings = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);

        document.getElementById(`originalSize-${index}`).textContent = this.formatFileSize(originalSize);
        document.getElementById(`compressedSize-${index}`).textContent = this.formatFileSize(compressedSize);
        document.getElementById(`savings-${index}`).textContent = `${savings}%`;

        compressionInfo.style.display = 'flex';
        downloadBtn.style.display = 'flex';

        downloadBtn.addEventListener('click', () => {
            this.downloadImage(compressedBlob, this.generateFileName(originalFile));
        });
    }

    generateFileName(originalFile) {
        const format = document.getElementById('formatSelect').value;
        const name = originalFile.name.split('.')[0];

        if (format === 'original') {
            return `${name}_compressed.${originalFile.name.split('.').pop()}`;
        }

        return `${name}_compressed.${format}`;
    }

    showProgressModal() {
        document.getElementById('progressModal').style.display = 'flex';
    }

    hideProgressModal() {
        document.getElementById('progressModal').style.display = 'none';
    }

    updateProgress(current, total) {
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');

        const percentage = (current / total) * 100;
        progressFill.style.width = `${percentage}%`;
        progressText.textContent = `${current} of ${total} images processed`;
    }

    showDownloadSection() {
        document.getElementById('downloadSection').style.display = 'block';
        document.getElementById('downloadSection').classList.add('fade-in');
    }

    downloadImage(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    async downloadAll() {
        if (this.compressedImages.length === 0) return;

        // Import JSZip dynamically
        const JSZip = await this.loadJSZip();
        const zip = new JSZip();

        this.compressedImages.forEach(({ blob, originalFile }) => {
            const filename = this.generateFileName(originalFile);
            zip.file(filename, blob);
        });

        const zipBlob = await zip.generateAsync({ type: 'blob' });
        this.downloadImage(zipBlob, 'compressed_images.zip');
    }

    async loadJSZip() {
        if (window.JSZip) return window.JSZip;

        return new Promise((resolve) => {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js';
            script.onload = () => resolve(window.JSZip);
            document.head.appendChild(script);
        });
    }

    clearAll() {
        this.selectedFiles = [];
        this.compressedImages = [];

        document.getElementById('controlsSection').style.display = 'none';
        document.getElementById('imagesGrid').style.display = 'none';
        document.getElementById('downloadSection').style.display = 'none';
        document.getElementById('fileInput').value = '';

        // Reset controls
        document.getElementById('qualitySlider').value = 80;
        document.getElementById('qualityValue').textContent = '80';
        document.getElementById('formatSelect').value = 'original';
        document.getElementById('resizeToggle').checked = false;
        document.getElementById('resizeControls').style.display = 'none';
        document.getElementById('widthInput').value = '';
        document.getElementById('heightInput').value = '';
        document.getElementById('maintainAspect').checked = true;

        this.showToast('All images cleared');
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    showToast(message, type = 'success') {
        const toastContainer = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        toastContainer.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}

// Initialize the app
document.addEventListener('DOMContentLoaded', () => {
    new ImageCompressor();
});

// Prevent default drag behaviors on the document
document.addEventListener('dragover', (e) => e.preventDefault());
document.addEventListener('drop', (e) => e.preventDefault());

// Additional utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Performance optimization for large images
function createImageBitmap(file) {
    return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => resolve(img);
        img.src = URL.createObjectURL(file);
    });
}

// Service Worker registration for offline functionality (optional)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('./sw.js')
            .then(() => console.log('SW registered'))
            .catch(() => console.log('SW registration failed'));
    });
}
