<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Compressor</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <div class="container">
        <header class="header">
            <h1 class="title">
                <i class="fas fa-compress-alt"></i>
                Image Compressor
            </h1>
            <p class="subtitle">Compress your images without losing quality</p>
        </header>

        <main class="main">
            <!-- Upload Section -->
            <div class="upload-section" id="uploadSection">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <i class="fas fa-cloud-upload-alt upload-icon"></i>
                        <h3>Drop your images here</h3>
                        <p>or click to browse</p>
                        <input type="file" id="fileInput" accept="image/*" multiple hidden>
                    </div>
                </div>
            </div>

            <!-- Controls Section -->
            <div class="controls-section" id="controlsSection" style="display: none;">
                <div class="control-group">
                    <label for="qualitySlider">Quality: <span id="qualityValue">80</span>%</label>
                    <input type="range" id="qualitySlider" min="10" max="100" value="80" class="slider">
                </div>

                <div class="control-group">
                    <label for="formatSelect">Output Format:</label>
                    <select id="formatSelect" class="select">
                        <option value="original">Keep Original</option>
                        <option value="jpeg">JPEG</option>
                        <option value="png">PNG</option>
                        <option value="webp">WebP</option>
                    </select>
                </div>

                <div class="control-group">
                    <label for="resizeToggle" class="checkbox-label">
                        <input type="checkbox" id="resizeToggle">
                        <span class="checkmark"></span>
                        Resize Images
                    </label>
                </div>

                <div class="resize-controls" id="resizeControls" style="display: none;">
                    <div class="resize-inputs">
                        <div class="input-group">
                            <label for="widthInput">Width (px)</label>
                            <input type="number" id="widthInput" placeholder="Auto" min="1">
                        </div>
                        <div class="input-group">
                            <label for="heightInput">Height (px)</label>
                            <input type="number" id="heightInput" placeholder="Auto" min="1">
                        </div>
                    </div>
                    <label for="maintainAspect" class="checkbox-label">
                        <input type="checkbox" id="maintainAspect" checked>
                        <span class="checkmark"></span>
                        Maintain Aspect Ratio
                    </label>
                </div>

                <div class="action-buttons">
                    <button id="compressBtn" class="btn btn-primary">
                        <i class="fas fa-compress"></i>
                        Compress Images
                    </button>
                    <button id="clearBtn" class="btn btn-secondary">
                        <i class="fas fa-trash"></i>
                        Clear All
                    </button>
                </div>
            </div>

            <!-- Images Grid -->
            <div class="images-grid" id="imagesGrid" style="display: none;">
                <!-- Images will be dynamically added here -->
            </div>

            <!-- Download Section -->
            <div class="download-section" id="downloadSection" style="display: none;">
                <button id="downloadAllBtn" class="btn btn-success">
                    <i class="fas fa-download"></i>
                    Download All Compressed Images
                </button>
            </div>
        </main>

        <!-- Progress Modal -->
        <div class="modal" id="progressModal" style="display: none;">
            <div class="modal-content">
                <h3>Compressing Images...</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <p id="progressText">0 of 0 images processed</p>
            </div>
        </div>

        <!-- Toast Notifications -->
        <div class="toast-container" id="toastContainer"></div>
    </div>

    <script src="script.js"></script>
</body>

</html>