# Image Compressor Web App

A modern, minimalistic, and mobile-responsive image compressor built with vanilla HTML, CSS, and JavaScript.

## Features

- **Drag & Drop Interface**: Simply drag and drop images or click to browse
- **Multiple Format Support**: Compress to JPEG, PNG, WebP, or keep original format
- **Quality Control**: Adjustable compression quality from 10% to 100%
- **Image Resizing**: Optional resizing with aspect ratio preservation
- **Batch Processing**: Compress multiple images at once
- **Download Options**: Download individual images or all as a ZIP file
- **Mobile Responsive**: Works perfectly on all device sizes
- **Offline Support**: Basic offline functionality with service worker
- **Real-time Preview**: See compression results and file size savings

## How to Use

1. **Upload Images**: 
   - Drag and drop image files onto the upload area
   - Or click the upload area to browse and select files
   - Supports multiple image selection

2. **Configure Settings**:
   - **Quality**: Adjust the compression quality (lower = smaller file, higher = better quality)
   - **Format**: Choose output format (JPEG, PNG, WebP, or keep original)
   - **Resize**: Optionally resize images by specifying width/height
   - **Aspect Ratio**: Maintain original proportions when resizing

3. **Compress**: Click "Compress Images" to process all selected images

4. **Download**: 
   - Download individual compressed images
   - Or download all as a ZIP file

## Technical Details

- **Pure Frontend**: No server required, all processing happens in the browser
- **Canvas API**: Uses HTML5 Canvas for image processing
- **Modern CSS**: Flexbox and Grid layouts for responsive design
- **Progressive Enhancement**: Works without JavaScript for basic functionality
- **Performance Optimized**: Efficient memory management for large images

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## File Structure

```
ImgCompressor/
├── index.html      # Main HTML structure
├── styles.css      # CSS styling and responsive design
├── script.js       # JavaScript functionality
├── sw.js          # Service worker for offline support
└── README.md      # This file
```

## Getting Started

1. Clone or download this repository
2. Open `index.html` in a web browser
3. Start compressing images!

No build process or dependencies required - just open and use!

## License

MIT License - feel free to use and modify as needed.
