/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.title {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.title i {
    font-size: 2rem;
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Main Content */
.main {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

/* Upload Section */
.upload-section {
    margin-bottom: 30px;
}

.upload-area {
    border: 3px dashed #e0e7ff;
    border-radius: 15px;
    padding: 60px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8faff;
}

.upload-area:hover {
    border-color: #667eea;
    background: #f0f4ff;
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-color: #667eea;
    background: #e0e7ff;
    transform: scale(1.02);
}

.upload-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #4f46e5;
    font-weight: 500;
}

.upload-content p {
    color: #6b7280;
    font-size: 1rem;
}

.upload-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
}

/* Controls Section */
.controls-section {
    background: #f8faff;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
}

.control-group {
    margin-bottom: 25px;
}

.control-group:last-child {
    margin-bottom: 0;
}

.control-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
}

/* Slider */
.slider {
    width: 100%;
    height: 8px;
    border-radius: 5px;
    background: #e5e7eb;
    outline: none;
    -webkit-appearance: none;
    cursor: pointer;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* Select */
.select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    background: white;
    font-size: 1rem;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.select:focus {
    outline: none;
    border-color: #667eea;
}

/* Checkbox */
.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 400;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-radius: 6px;
    margin-right: 12px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked+.checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked+.checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 12px;
}

/* Resize Controls */
.resize-controls {
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    border: 1px solid #e5e7eb;
}

.resize-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.input-group label {
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.input-group input {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* Buttons */
.action-buttons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.btn {
    padding: 14px 28px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    justify-content: center;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #f3f4f6;
    color: #6b7280;
}

.btn-secondary:hover {
    background: #e5e7eb;
    transform: translateY(-2px);
}

.btn-success {
    background: #10b981;
    color: white;
    width: 100%;
    margin-top: 20px;
}

.btn-success:hover {
    background: #059669;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Images Grid */
.images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.image-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.image-card:hover {
    transform: translateY(-4px);
}

.image-preview {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 15px;
}

.image-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.image-name {
    font-weight: 500;
    color: #374151;
    font-size: 0.9rem;
    truncate: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.image-size {
    font-size: 0.8rem;
    color: #6b7280;
}

.compression-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f0f4ff;
    border-radius: 8px;
    font-size: 0.85rem;
}

.original-size {
    color: #ef4444;
}

.compressed-size {
    color: #10b981;
    font-weight: 500;
}

.savings {
    color: #667eea;
    font-weight: 600;
}

.download-btn {
    width: 100%;
    padding: 10px;
    background: #f3f4f6;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 10px;
}

.download-btn:hover {
    background: #e5e7eb;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.modal-content h3 {
    margin-bottom: 20px;
    color: #374151;
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: #667eea;
    width: 0%;
    transition: width 0.3s ease;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
}

.toast {
    background: #10b981;
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateX(100%);
    animation: slideIn 0.3s ease forwards;
}

.toast.error {
    background: #ef4444;
}

@keyframes slideIn {
    to {
        transform: translateX(0);
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .main {
        padding: 25px;
    }

    .title {
        font-size: 2rem;
    }

    .upload-area {
        padding: 40px 15px;
    }

    .action-buttons {
        flex-direction: column;
    }

    .resize-inputs {
        grid-template-columns: 1fr;
    }

    .images-grid {
        grid-template-columns: 1fr;
    }

    .image-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

@media (max-width: 480px) {
    .title {
        font-size: 1.8rem;
        flex-direction: column;
        gap: 10px;
    }

    .subtitle {
        font-size: 1rem;
    }

    .main {
        padding: 20px;
    }

    .modal-content {
        padding: 30px 20px;
    }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }

    100% {
        left: 100%;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mt-20 {
    margin-top: 20px;
}

.mb-20 {
    margin-bottom: 20px;
}